
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Title</title>
    </head>
    <body>
        <table  style="border-collapse: collapse" border="0" cellspacing="0" cellpadding="0"><colgroup>
<col  style="width: 296.64px">
<col  style="width: 516.48px">
<col  style="width: 474.24px">
<col  style="width: 207.36px">
</colgroup>
<tr>
<td id="Sheet1!A1" style="background-color: #92D050;border-bottom: none;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 11.0px;height: 19pt;text-align: center;vertical-align: center">测试名称</td>
<td id="Sheet1!B1" style="background-color: #92D050;border-bottom: none;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 11.0px;height: 19pt;text-align: center;vertical-align: center">测试步骤</td>
<td id="Sheet1!C1" style="background-color: #92D050;border-bottom: none;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right: none;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 11.0px;height: 19pt;text-align: center;vertical-align: center">测试标准</td>
<td id="Sheet1!D1" style="background-color: #92D050;border-bottom: none;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right: none;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 11.0px;height: 19pt;text-align: center;vertical-align: center">不支持说明</td>
</tr>
<tr>
<td id="Sheet1!A2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 11.0px;height: 283.5pt;vertical-align: center">PXE功能-安装baidu centos7u6
</td>
<td id="Sheet1!B2" style="border-bottom-color: #000000;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-color: #000000;border-left-style: solid;border-left-width: 1px;border-right-color: #000000;border-right-style: solid;border-right-width: 1px;border-top-color: #000000;border-top-style: solid;border-top-width: 1px;font-size: 11.0px;height: 283.5pt;vertical-align: center">1、default BIOS下，确认PXE功能是否默认为enable。
2.、设置硬盘（空盘）为第一启动项，网络为第二启动项。
3、检查在多网卡配置时，主网卡为网络启动项的第一个位置，比如GPU 8+1配置，带智能网卡配置，智能网卡为网络启动项里第一位置。
4、机器AC掉电，上电后，服务器自动进入PXE，能够正常安装百度系统centos7u6。
5、安装完成后的系统，执行power reset 5次，每次都能正常进入系统。
6、重复步骤2，机器执行ipmitool power reset，服务器自动进入PXE，能够正常安装系统，并且进系统使用ip a 指令检查第一个网口主网卡。
7、重复步骤2，机器执行扩展电源cycle（raw 0x30 0x02 0x61 0x7e 0x00 0x2），服务器自动进入PXE，能够正常安装系统，并且进系统使用ip a 指令检查第一个网口主网卡。
Log要求：
截图/抓取保存BIOS启动项图片，进PXE界面日志，及正常进入安装后的系统日志。

主网卡定义：
BF3网卡配置：主网卡为BF3</td>
<td id="Sheet1!C2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right: none;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 11.0px;height: 283.5pt;vertical-align: center">1. 主网卡为网络启动项的第一个位置，比如GPU 8+1配置，1张以太网卡默认为网络启动项里第一位置，否则测试fail。带智能网卡配置，智能网卡为网络启动项里第一位置。
2. 机器AC掉电，上电后，服务器自动进入PXE，能够正常安装系统。
3. 机器执行ipmitool power reset，服务器自动进入PXE，能够正常安装系统。
4. 机器执行扩展电源cycle（raw 0x30 0x02 0x61 0x7e 0x00 0x2），服务器自动进入PXE，能够正常安装系统。</td>
<td id="Sheet1!D2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 11.0px;height: 283.5pt;vertical-align: center">&nbsp;</td>
</tr>
<tr>
<td id="Sheet1!A3" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 11.0px;height: 324.0pt;vertical-align: center">PXE功能-安装CN镜像</td>
<td id="Sheet1!B3" style="border-bottom-color: #000000;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-color: #000000;border-left-style: solid;border-left-width: 1px;border-right-color: #000000;border-right-style: solid;border-right-width: 1px;border-top-color: #000000;border-top-style: solid;border-top-width: 1px;font-size: 11.0px;height: 324.0pt;vertical-align: center">1. 配置百度PXE环境，增加CN镜像，CN镜像下载位置：https://hqc.baidu.com/?code=CT-845302786899570689-HVG1TsHhkm-online&state=hqcauth#/resLib/3/20/resources/3/231883  ,配置方法同baidu centos7u6，cnetos6u3等。
2、default BIOS下，确认PXE功能是否默认为enable。
3.、设置硬盘（空盘）为第一启动项，网络为第二启动项。
4. 检查在多网卡配置时，主网卡为网络启动项的第一个位置，比如GPU 8+1配置，带智能网卡配置，智能网卡为网络启动项里第一位置。
5、机器AC掉电，上电后，服务器自动进入PXE，能够正常安装系统。
6、重复步骤2，机器执行ipmitool power reset 5次，服务器自动进入PXE，能够正常安装系统，并且进系统使用ip a 指令检查第一个网口主网卡。
7、重复步骤2，机器执行扩展电源cycle（raw 0x30 0x02 0x61 0x7e 0x00 0x2），服务器自动进入PXE，能够正常安装系统，并且进系统使用ip a 指令检查第一个网口主网卡。
Log要求：
截图/抓取保存BIOS启动项图片，进PXE界面日志，及正常进入安装后的系统日志。

主网卡定义：
智能网卡配置：主网卡为智能网卡</td>
<td id="Sheet1!C3" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right: none;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 11.0px;height: 324.0pt;vertical-align: center">1. 主网卡为网络启动项的第一个位置，比如GPU 8+1配置，1张以太网卡默认为网络启动项里第一位置，否则测试fail。带智能网卡配置，智能网卡为网络启动项里第一位置。
2. 机器AC掉电，上电后，服务器自动进入PXE，能够正常安装系统。
3. 机器执行ipmitool power reset，服务器自动进入PXE，能够正常安装系统。
4. 机器执行扩展电源cycle（raw 0x30 0x02 0x61 0x7e 0x00 0x2），服务器自动进入PXE，能够正常安装系统。</td>
<td id="Sheet1!D3" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 11.0px;height: 324.0pt;vertical-align: center">&nbsp;</td>
</tr>
</table>
    </body>
    </html>
    