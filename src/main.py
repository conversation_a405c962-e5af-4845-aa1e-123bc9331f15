import os
import copy
import shutil
import xlsx2html
from datetime import datetime
from typing import Union, List

from src.utils.delete_contents import delete_contents, delete_html_files, delete_xlsx_files
from src.utils.download_log import download_testcase_log_by_run_id
from src.utils.parse_log_testcase_info import get_log_testcase_info, get_manual_testcase_info
from src.utils.write_excel import wirte_test_case_result_to_excel, wirte_manual_tc_result_to_excel
from src.utils.generate_index_html import generate_index_html
from src.utils.generate_statistics_html import generate_statistics_html
from src.utils.clear_log_error import clear_statistics_and_errors_by_folder
from src.utils.generate_all_pass_log import generate_all_pass_log


ALL_TEST_TYPE_LIST = ["BIOS", "BMC", "REDFISH", "STABILITY", "PERFORMANCE", "FRU", "OPERATION"]

TEST_SHEET_NAME_SAC_IDX ={
    ("BIOS", "EGS 2U"): ("1.BIOS定制化测试（EGS 2U）", 6, 8),
    ("BIOS", "EGS GPU"): ("1.BIOS定制化测试（EGS GPU）", 6, 8),
    ("BIOS", "Genoa"): ("1.BIOS定制化测试（Genoa）", 6, 8),
    ("BIOS", "Whitley"): ("1.BIOS定制化测试（Whitley）", 6, 8),
    ("BIOS", "Milan"): ("1.BIOS定制化测试（Milan）", 6, 8),
    ("BIOS", "HG4"): ("1.BIOS定制化测试（HG4）", 6, 8),
    ("BMC", ""): ("2.BMC定制化测试", 4, 6),
    ("REDFISH", ""): ("3.Redfish测试", 7, 9),
    ("STABILITY", ""): ("4.稳定性测试", 4, 6),
    ("PERFORMANCE", ""): ("5.性能测试", 4, 6),
    ("FRU", ""): ("6.FRU信息测试", 8, 10),
    ("OPERATION", ""): ("7.可运维测试", 6, 7)
}


def generate_report_html_by_run_id(runIds:List, machineType:str="EGS", testTypes:Union[str, List[str]]="ALL",
                                   autologPath:str="./autologs", isClearAutologs:bool=False, hasManualTc=True):
    """ (1)根据测试管理平台的runId, 从ftp下载对应日志文件到本地
        (2)分析日志文件并将测试结果写入测试表格
        (3)生成对应测试报告的html文件
        (4)汇聚所有测试报告html文件到索引html文件
    Args:
        runIds (List): 测试管理测试平台执行ID,用于获取测试报告ftp路径
        machineType (str, optional): 平台类型. 默认值 "EGS". 可以传 "EGS"  "Renoa"  "Whitley"  "Milan"
        testTypes (Union[str, List[str]], optional):测试类型. 默认值 "ALL", 还可以传元素为 "BIOS" "BMC" "REDFISH" "STABILITY" "PERFORMANCE" "FRU" 的列表
        autologPath (str, optional): 自动化日志路径. Defaults to "./autologs".
        isClearAutologs (bool, optional): 是否清除自动化日志路径的文件. Defaults to False.
    """
    autologPath = os.path.abspath(autologPath)
    # 获取 sheet名 和 用例标识、自动化结果的索引
    sheetInfoList = _get_sheet_info(testTypes, machineType)
    if isClearAutologs:
        # 清空日志文件夹
        delete_contents(autologPath)
    else:
        delete_html_files(autologPath)
    # 下载日志
    tempPath = os.path.join(os.path.dirname(autologPath), "autologs_temp")
    if not os.path.exists(tempPath):
        os.makedirs(tempPath)
    for runId in runIds:
        download_testcase_log_by_run_id(runId, tempPath)
    clear_statistics_and_errors_by_folder(tempPath, True)
    # generate_all_pass_log(tempPath)
    if runIds and os.path.exists(tempPath) and os.path.exists(autologPath):
        os.system(f"mv {tempPath}/* {autologPath}/")

    # 变量 autologPath 获取所有用例信息
    logTestcasesInfo = get_log_testcase_info("./autologs")

    if hasManualTc:
        manualTcInfo = get_manual_testcase_info("./manual")
        print(manualTcInfo)

    # 生成对应sheet页的html文件
    for sheetName, sacIdx, resultIdx in sheetInfoList:
        resultExcelFile = _generate_test_excel_file(sheetName)
        wirte_test_case_result_to_excel(logTestcasesInfo, resultExcelFile, sheetName, sacIdx, resultIdx)
        if hasManualTc:
            wirte_manual_tc_result_to_excel(manualTcInfo, resultExcelFile, sheetName, sacIdx, resultIdx)
        htmlFile = resultExcelFile.replace("xlsx", "html")
        xlsx2html.xlsx2html(resultExcelFile, htmlFile, sheet=sheetName)
        print(f"----- {htmlFile} 已经生成 -----")
    generate_statistics_html(autologPath)
    delete_xlsx_files(autologPath)
    generate_index_html(autologPath)
    replace_index_html_name()

def replace_index_html_name():
    from datetime import datetime
    from src.versionInfo import VERSION_INFO
    generateTime = datetime.now().strftime("%Y年%m月%d日")
    os.rename("./index.html", f"{VERSION_INFO['machineType']}-BmcVersion_{VERSION_INFO['bmcVersion']}-biosVersion_{VERSION_INFO['biosVersion']}-{generateTime}.html")

def _get_sheet_info(testTypes, machineType):
    if (testTypes == "ALL" or "BIOS" in testTypes) and not machineType:
        raise Exception("参数 machineType 不能为空！")
    testTypeList = []
    if testTypes == "ALL":
        testTypeList = copy.deepcopy(ALL_TEST_TYPE_LIST)
    elif isinstance(testTypes, list):
        testTypeList = testTypes
    else:
        raise Exception("testTypes 参数填写错误!")
    if not set(testTypeList).issubset(set(ALL_TEST_TYPE_LIST)):
        raise Exception("testTypes 参数填写错误!")
    testItems = []
    for testType in testTypeList:
        if testType == "BIOS":
            testItems.append((testType, machineType))
        else:
            testItems.append((testType, ""))
    sheetInfoList = []
    for testItem in testItems:
        sheetInfoList.append(TEST_SHEET_NAME_SAC_IDX[testItem])
    return sheetInfoList

def _generate_test_excel_file(sheetName):
    timestamp = datetime.now().strftime('%y%m%d%H%M%S')
    sourceFile = './src/templates/京东云服务器整机选型测试规范V1.5_SAC用例对应关系v0.4-250424.xlsx'
    destinationFile = f'./autologs/京东云服务器整机选型测试_{sheetName}-{timestamp}.xlsx'
    shutil.copy(sourceFile, destinationFile)
    return destinationFile


if __name__ == "__main__":
    # runIds = [ 69237]
    runIds = []
    generate_report_html_by_run_id(runIds, 'EGS GPU', testTypes="ALL", isClearAutologs=False, hasManualTc=False)
    print("-----end-----")

