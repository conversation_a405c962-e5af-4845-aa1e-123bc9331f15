<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用例执行统计报告</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f4f7fa;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            width: 80%;
            margin: 20px auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #4CAF50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px 15px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 14px;
        }
        th {
            background-color: #4CAF50;
            color: white;
        }
        td {
            background-color: #f9f9f9;
        }
        td.pass {
            background-color: #c8e6c9;
            color: #388e3c;
        }
        td.fail {
            background-color: #ffccbc;
            color: #d32f2f;
        }
        td.noSupport {
            background-color: #e0e0e0;
            color: #757575;
        }
        .footer {
            text-align: center;
            font-size: 14px;
            margin-top: 30px;
            color: #777;
        }
        .changelist {
            text-align: center;
            font-size: 14px;
            margin-top: 10px;
            color: #777;
        }
        .container2 {
            width: 80%;
            margin: 20px auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .info-list {
            list-style-type: none; /* 去掉列表的默认样式 */
            padding: 0;
            margin: 0;
            font-family: Arial, sans-serif; /* 设置字体 */
        }

        .info-item {
            margin-bottom: 10px;
            display: flex;
            align-items: center; /* 垂直居中对齐 */
            height: 40px; /* 固定高度 */
            border-bottom: 1px solid #ddd; 
            width: 100%; /* 宽度固定为100% */
        }

        .info-label {
            width: 110px;
            font-weight: bold; /* 加粗标签 */
            margin-right: 10px; /* 标签与值之间的间距 */
            font-size: 16px; /* 标签字体大小 */
        }

        .info-value {
            font-size: 15px; /* 值字体大小 */
            color: #333; /* 值的颜色 */
        }
        .info {
            display: inline-block;
            margin: 0 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }

        .tooltip-content {
        visibility: hidden;
        position: fixed; /* 改为 fixed 定位 */
        z-index: 1000;
        background-color: white;
        border: 1px solid #ccc;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.2);
        
        /* 设置宽高为视窗的90% */
        width: 90vw;
        max-height: 85vh;
        
        /* 居中定位 */
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        
        /* 其他样式 */
        text-align: left;
        white-space: pre-wrap;       
        word-wrap: break-word;      
        overflow-wrap: break-word;  
        font-family: monospace;
        font-size: 12px;      
        line-height: 1.4;     
        overflow-y: auto;

        /* 添加平滑过渡 */
        transition: visibility 0.2s ease, opacity 0.2s ease;
        opacity: 0;
    }

    /* 内容容器 */
    .tooltip-content-inner {
        width: 80%;
        margin: 0 auto;
    }
    
    .tooltip:hover .tooltip-content {
        visibility: visible;
        opacity: 1;
    }

    /* 添加遮罩背景 */
    .tooltip-content::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0);
        z-index: -1;
    }
        /* 图片样式 */
    .tooltip-image {
        max-width: 100%;  /* 图片最大宽度不超过容器 */
        height: auto;     /* 保持图片比例 */
        display: block;   /* 块级显示避免底部间隙 */
        margin: 10px auto; /* 上下间距和水平居中 */
        border-radius: 4px; /* 圆角效果 */
        box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* 轻微阴影 */
    }
    </style>
</head>
<body>
    <div class="container">
        <h1>用例执行统计报告</h1>
        <div class="changelist">
            ###changeListData###
        </div>
        <table>
            <thead>
                <tr>
                    <th>测试项</th>
                    <th>用例执行总数</th>
                    <th>通过</th>
                    <th>不通过</th>
                    <th>不支持</th>
                    <th>通过率</th>
                </tr>
            </thead>
            <tbody>
                ###statisticsData###
            </tbody>
        </table>
    </div>
</body>
</html>
