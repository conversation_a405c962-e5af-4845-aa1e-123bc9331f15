<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用例执行统计报告</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f4f7fa;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            width: 80%;
            margin: 20px auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #4CAF50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px 15px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 14px;
        }
        th {
            background-color: #4CAF50;
            color: white;
        }
        td {
            background-color: #f9f9f9;
        }
        td.pass {
            background-color: #c8e6c9;
            color: #388e3c;
        }
        td.fail {
            background-color: #ffccbc;
            color: #d32f2f;
        }
        td.noSupport {
            background-color: #e0e0e0;
            color: #757575;
        }
        .footer {
            text-align: center;
            font-size: 14px;
            margin-top: 30px;
            color: #777;
        }
        .changelist {
            text-align: center;
            font-size: 14px;
            margin-top: 10px;
            color: #777;
        }
        .container2 {
            width: 80%;
            margin: 20px auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .info-list {
            list-style-type: none; /* 去掉列表的默认样式 */
            padding: 0;
            margin: 0;
            font-family: Arial, sans-serif; /* 设置字体 */
        }

        .info-item {
            margin-bottom: 10px;
            display: flex;
            align-items: center; /* 垂直居中对齐 */
            height: 40px; /* 固定高度 */
            border-bottom: 1px solid #ddd; 
            width: 100%; /* 宽度固定为100% */
        }

        .info-label {
            width: 110px;
            font-weight: bold; /* 加粗标签 */
            margin-right: 10px; /* 标签与值之间的间距 */
            font-size: 16px; /* 标签字体大小 */
        }

        .info-value {
            font-size: 15px; /* 值字体大小 */
            color: #333; /* 值的颜色 */
        }
        .info {
            display: inline-block;
            margin: 0 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
        }

        /* 可点击数字样式 */
        .clickable-number {
            text-decoration: none;
            cursor: pointer;
            color: inherit;
        }

        .clickable-number:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>用例执行统计报告</h1>
        <div class="changelist">
            ###changeListData###
        </div>
        <table>
            <thead>
                <tr>
                    <th>测试项</th>
                    <th>用例执行总数</th>
                    <th>通过</th>
                    <th>不通过</th>
                    <th>不支持</th>
                    <th>通过率</th>
                </tr>
            </thead>
            <tbody>
                ###statisticsData###
            </tbody>
        </table>
    </div>

    <!-- 全局模态框 -->
    <div id="globalModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div class="modal-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.5); width: 80%; max-width: 800px; max-height: 80vh; overflow: hidden;">
            <iframe id="modalIframe" src="" style="width: 100%; height: 500px; border: none;"></iframe>
        </div>
    </div>

    <script>
    function showIframe(src) {
        var modal = document.getElementById('globalModal');
        var iframe = document.getElementById('modalIframe');
        iframe.src = src;
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // 防止背景滚动
    }

    function closeModal() {
        var modal = document.getElementById('globalModal');
        var iframe = document.getElementById('modalIframe');
        modal.style.display = 'none';
        iframe.src = ''; // 清空iframe内容
        document.body.style.overflow = 'auto'; // 恢复背景滚动
    }

    // 点击模态框外部关闭
    document.getElementById('globalModal').addEventListener('click', function(e) {
        if(e.target === this) {
            closeModal();
        }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if(e.key === 'Escape') {
            closeModal();
        }
    });
    </script>
</body>
</html>
