import os
import platform
import subprocess
import fnmatch


def delete_contents(foldePath):
    try:
        if platform.system() == 'Windows':
            subprocess.run(['rmdir', '/s', '/q', foldePath], check=True)
        else:
            for item in os.listdir(foldePath):
                item_path = os.path.join(foldePath, item)
                if os.path.isfile(item_path):
                    os.remove(item_path)
                elif os.path.isdir(item_path):
                    subprocess.run(['rm', '-rf', item_path], check=True)
        print(f"成功删除 {foldePath} 下的所有内容。")
    except subprocess.CalledProcessError as e:
        print(f"删除失败: {e}")


def delete_html_files(folderPath):
    for filename in os.listdir(folderPath):
        if filename.endswith('.html'):
            filePath = os.path.join(folderPath, filename)
            try:
                os.remove(filePath)
                print(f"已删除文件: {filePath}")
            except Exception as e:
                print(f"删除文件 {filePath} 失败: {e}")


def delete_xlsx_files(folderPath):
    for filename in os.listdir(folderPath):
        if filename.endswith('.xlsx'):
            filePath = os.path.join(folderPath, filename)
            try:
                os.remove(filePath)
                print(f"已删除文件: {filePath}")
            except Exception as e:
                print(f"删除文件 {filePath} 失败: {e}")


def delete_xml_files(folder_path):
    for root, dirs, files in os.walk(folder_path):
        for filename in fnmatch.filter(files, '*.xml'):
            file_path = os.path.join(root, filename)
            try:
                os.remove(file_path)
                print(f"已删除文件: {file_path}")
            except Exception as e:
                print(f"删除文件 {file_path} 失败: {e}")


if __name__ == "__main__":
    # folderToClean = '/home/<USER>/Desktop/generate_report/autologs'
    # folderToClean = '/home/<USER>/Desktop/generate_report/R5300G5/autologs'
    # folderToClean = '/home/<USER>/Desktop/generate_report/R5350G5/autologs'
    folderToClean = '/home/<USER>/Desktop/leting data/generate_report/report_250429-2/autologs'
    delete_xml_files(folderToClean)
