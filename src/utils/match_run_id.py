str1 = '''https://wxict.zte.com.cn:8000/#/test/automation/report/59627
https://wxict.zte.com.cn:8000/#/test/automation/report/59627
https://wxict.zte.com.cn:8000/#/test/automation/report/59627
https://wxict.zte.com.cn:8000/#/test/automation/report/59627
https://wxict.zte.com.cn:8000/#/test/automation/report/59627
https://wxict.zte.com.cn:8000/#/test/automation/report/59627
https://wxict.zte.com.cn:8000/#/test/automation/report/59629
'''


import re

result = re.findall("report/(\d+)", str1)

print(list(set(result)))



